# 导入数学计算库，用于权重初始化时的计算，如sqrt(2/n)等
import math
# 导入操作系统库，用于处理文件路径和目录操作，如创建model_data文件夹
import os

# 导入PyTorch深度学习框架，提供张量运算和自动求导功能
import torch
# 导入PyTorch的神经网络模块，包含各种网络层和损失函数
import torch.nn as nn
# 导入PyTorch的模型加载工具，用于下载和加载预训练模型
import torch.utils.model_zoo as model_zoo

# 为nn.BatchNorm2d创建一个别名BatchNorm2d，使代码更简洁
# BatchNorm2d用于对卷积层的输出进行批归一化，提高训练稳定性
BatchNorm2d = nn.BatchNorm2d

# 定义标准卷积块函数，包含卷积、批归一化和ReLU6激活
def conv_bn(inp, oup, stride):
    # 返回一个顺序容器，包含三个层：卷积、批归一化和ReLU6激活
    return nn.Sequential(
        # 2D卷积层，用于特征提取
        # inp: 输入通道数
        # oup: 输出通道数
        # 3: 卷积核大小为3x3
        # stride: 步长，控制输出特征图的大小
        # padding=1: 填充1个像素，保持特征图大小
        # bias=False: 不使用偏置项，因为后面有批归一化
        nn.Conv2d(inp, oup, 3, stride, 1, bias=False),
        # 批归一化层，对卷积输出进行归一化，提高训练稳定性
        # oup: 输入通道数，与卷积输出通道数相同
        BatchNorm2d(oup),
        # ReLU6激活函数，将输出限制在[0,6]范围内，提高模型鲁棒性
        nn.ReLU6(inplace=True)
    )

# 定义1x1卷积块函数，用于通道数调整和特征融合
def conv_1x1_bn(inp, oup):
    # 返回一个顺序容器，包含三个层：1x1卷积、批归一化和ReLU6激活
    return nn.Sequential(
        # 1x1卷积层，用于调整通道数和特征融合
        # inp: 输入通道数
        # oup: 输出通道数
        # 1: 卷积核大小为1x1
        # bias=False: 不使用偏置项
        nn.Conv2d(inp, oup, 1, 1, 0, bias=False),
        # 批归一化层，对卷积输出进行归一化
        BatchNorm2d(oup),
        # ReLU6激活函数，将输出限制在[0,6]范围内
        nn.ReLU6(inplace=True)
    )

# 定义倒置残差块类，这是MobileNetV2的核心构建块
class InvertedResidual(nn.Module):
    # 初始化函数，设置倒置残差块的参数
    def __init__(self, inp, oup, stride, expand_ratio):
        # 调用父类nn.Module的初始化函数
        super(InvertedResidual, self).__init__()
        # 保存步长参数，用于判断是否使用残差连接
        self.stride = stride
        # 断言确保步长只能是1或2
        assert stride in [1, 2]

        # 计算隐藏层的通道数，用于扩展特征维度
        # 如果expand_ratio为1，则隐藏层通道数等于输入通道数
        # 否则，隐藏层通道数等于输入通道数乘以扩展比例
        hidden_dim = round(inp * expand_ratio)
        # 判断是否使用残差连接
        # 当步长为1且输入输出通道数相同时，可以使用残差连接
        self.use_res_connect = self.stride == 1 and inp == oup

        # 定义网络层
        layers = []
        # 如果扩展比例不为1，添加1x1卷积进行通道扩展
        if expand_ratio != 1:
            # 使用1x1卷积将输入通道扩展到hidden_dim         目的是提升通道数
            layers.append(conv_1x1_bn(inp, hidden_dim))
        # 添加深度可分离卷积层
        layers.extend([
            # 深度卷积层，对每个通道单独进行卷积
            # groups=hidden_dim表示每个通道独立卷积
            # 3x3卷积核，步长为stride，padding=1保持特征图大小       目的是进行跨特征点的特征提取
            nn.Conv2d(hidden_dim, hidden_dim, 3, stride, 1, groups=hidden_dim, bias=False),
            # 批归一化层
            BatchNorm2d(hidden_dim),
            # ReLU6激活函数
            nn.ReLU6(inplace=True),
            # 1x1卷积层，将通道数从hidden_dim压缩到oup       目的是降低通道数 减少运算量
            nn.Conv2d(hidden_dim, oup, 1, 1, 0, bias=False),
            # 批归一化层
            BatchNorm2d(oup),
        ])
        # 将所有层组合成一个顺序容器
        self.conv = nn.Sequential(*layers)

    # 前向传播函数
    def forward(self, x):
        # 如果可以使用残差连接，则返回输入加上卷积结果
        if self.use_res_connect:
            return x + self.conv(x)
        # 否则只返回卷积结果
        else:
            return self.conv(x)

# 定义MobileNetV2网络类
class MobileNetV2(nn.Module):
    # 初始化函数，设置网络参数
    def __init__(self, n_class=1000, input_size=224, width_mult=1.):
        # 调用父类nn.Module的初始化函数
        super(MobileNetV2, self).__init__()
        # 设置输入图像大小
        input_channel = 32
        # 设置最后一层特征图的通道数
        last_channel = 1280

        # 定义网络配置，每个元素包含：
        # t: 扩展因子
        # c: 输出通道数 通道数越多，网络可以提取的特征越丰富
        # n: 重复次数
        # s: 步长
        interverted_residual_setting = [
            # t, c, n, s   t表示是输入通道数是否上升 c表示是输出通道数 n表示每一个列表循环次数 s表示是否对高和宽压缩（2表示进行压缩）
            [1, 16, 1, 1],    # 第一层：扩展因子1，输出16通道，重复1次，步长1  256x256x32 > 256x256x16
            [6, 24, 2, 2],    # 第二层：扩展因子6，输出24通道，重复2次，步长2  256x256x16 > 128x128x24  下采样位置：2=1（列表之前有一次）+1  只看n
            [6, 32, 3, 2],    # 第三层：扩展因子6，输出32通道，重复3次，步长2  128x128x24 > 64x64x32    下采样位置：4=1+1+2
            [6, 64, 4, 2],    # 第四层：扩展因子6，输出64通道，重复4次，步长2  64x64x32 > 32x32x64      下采样位置：7=1+1+2+3
            [6, 96, 3, 1],    # 第五层：扩展因子6，输出96通道，重复3次，步长1  32x32x64 > 32x32x96    
            [6, 160, 3, 2],   # 第六层：扩展因子6，输出160通道，重复3次，步长2 32x32x96 > 16x16x160     下采样位置：14=1+1+2+3+4+3
            [6, 320, 1, 1],   # 第七层：扩展因子6，输出320通道，重复1次，步长1 16x16x160 > 16x16x320
        ]

        # 确保输入尺寸是32的倍数
        assert input_size % 32 == 0
        # 根据width_mult调整通道数
        input_channel = int(input_channel * width_mult)
        self.last_channel = int(last_channel * width_mult) if width_mult > 1.0 else last_channel
        
        # 构建第一层  目的是对输入进来的特征层进行一次高和宽的压缩 512x512x3转换为256x256x32的特征层
        self.features = [conv_bn(3, input_channel, 2)]
        
        # 构建倒置残差块 目的是进行特征循环提取
        for t, c, n, s in interverted_residual_setting:
            output_channel = int(c * width_mult)
            for i in range(n):
                if i == 0:
                    self.features.append(InvertedResidual(input_channel, output_channel, s, t))
                else:
                    self.features.append(InvertedResidual(input_channel, output_channel, 1, t))
                input_channel = output_channel
        
        # 构建最后几层
        self.features.append(conv_1x1_bn(input_channel, self.last_channel))
        # 将特征提取层转换为Sequential
        self.features = nn.Sequential(*self.features)

        # 构建分类器
        self.classifier = nn.Sequential(
            # Dropout层，防止过拟合
            nn.Dropout(0.2),
            # 全连接层，将特征图转换为类别概率
            nn.Linear(self.last_channel, n_class),
        )

        # 初始化权重
        self._initialize_weights()

    # 前向传播函数
    def forward(self, x):
        """
        前向传播
        1. 特征提取
        2. 全局平均池化
        3. 分类
        """
        x = self.features(x)
        x = x.mean(3).mean(2)  # 全局平均池化
        x = self.classifier(x)
        return x

    def _initialize_weights(self):
        """
        初始化网络权重
        使用不同的初始化方法：
        1. 卷积层：使用正态分布初始化
        2. BatchNorm层：权重初始化为1，偏置初始化为0
        3. 全连接层：使用较小的正态分布初始化
        """
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                m.weight.data.normal_(0, math.sqrt(2. / n))
                if m.bias is not None:
                    m.bias.data.zero_()
            elif isinstance(m, BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()
            elif isinstance(m, nn.Linear):
                n = m.weight.size(1)
                m.weight.data.normal_(0, 0.01)
                m.bias.data.zero_()

# 定义模型权重下载URL
model_urls = {
    'mobilenetv2': 'http://sceneparsing.csail.mit.edu/model/pretrained_resnet/mobilenet_v2.pth.tar',
}

# 定义模型加载函数
def load_url(url, model_dir='./model_data'):
    # 如果模型目录不存在，则创建
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)
    # 从URL下载模型权重
    state_dict = model_zoo.load_url(url, model_dir=model_dir)
    return state_dict

# 定义创建MobileNetV2模型的函数
def mobilenetv2(pretrained=False, **kwargs):
    # 创建MobileNetV2模型实例
    model = MobileNetV2(**kwargs)
    # 如果指定使用预训练权重
    if pretrained:
        # 加载预训练权重
        model.load_state_dict(load_url(model_urls['mobilenetv2']), strict=False)
    return model 