#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的代码是否正确处理9个类别（不包含背景类）
"""

import numpy as np
import os

def test_json_to_dataset_classes():
    """测试json_to_dataset.py中的类别定义"""
    print("=== 测试 json_to_dataset.py 类别定义 ===")
    
    # 模拟json_to_dataset.py中的classes定义
    classes = ["daolu", "dapeng", "gengdi", "guanmu", "jiansheyongdi", "lindi", "luodi", "yantang", "yvmidi"]
    
    print(f"类别数量: {len(classes)}")
    print(f"类别列表: {classes}")
    
    # 验证类别编号
    for i, class_name in enumerate(classes):
        print(f"类别 '{class_name}' -> 标签值: {i + 1}")  # 在新的实现中，标签值从1开始
    
    print("未标注区域 -> 标签值: 0")
    print()

def test_train_config():
    """测试训练配置"""
    print("=== 测试训练配置 ===")

    num_classes = 9  # 来自train.py
    print(f"训练时的类别数量: {num_classes}")

    # 验证这与实际类别数量匹配
    classes = ["daolu", "dapeng", "gengdi", "guanmu", "jiansheyongdi", "lindi", "luodi", "yantang", "yvmidi"]
    assert len(classes) == num_classes, f"类别数量不匹配: {len(classes)} != {num_classes}"
    print("✓ 类别数量匹配")
    print()

def test_dataloader_processing():
    """测试数据加载器的标签处理"""
    print("=== 测试数据加载器标签处理 ===")

    # 模拟原始标签数据
    original_labels = np.array([
        [0, 1, 2, 3],  # 未标注, 道路, 大棚, 耕地
        [4, 5, 6, 7],  # 灌木, 建设用地, 林地, 裸地
        [8, 9, 0, 1]   # 盐塘, 育苗地, 未标注, 道路
    ])

    print("原始标签:")
    print(original_labels)

    # 模拟dataloader中的处理逻辑
    processed_labels = original_labels.copy()
    processed_labels[original_labels == 0] = 255  # 未标注区域设为忽略标签
    processed_labels[original_labels > 0] = original_labels[original_labels > 0] - 1  # 1-9变成0-8

    print("处理后的标签:")
    print(processed_labels)

    print("标签映射关系:")
    print("0(未标注) -> 255(忽略)")
    classes = ["daolu", "dapeng", "gengdi", "guanmu", "jiansheyongdi", "lindi", "luodi", "yantang", "yvmidi"]
    for i, class_name in enumerate(classes):
        print(f"{i+1}({class_name}) -> {i}")
    print()

def test_prediction_output():
    """测试预测输出处理"""
    print("=== 测试预测输出处理 ===")

    # 模拟网络输出（argmax后的结果，范围0-8）
    network_output = np.array([
        [0, 1, 2, 3],  # 对应道路, 大棚, 耕地, 灌木
        [4, 5, 6, 7],  # 对应建设用地, 林地, 裸地, 盐塘
        [8, 0, 1, 2]   # 对应育苗地, 道路, 大棚, 耕地
    ])

    print("网络输出(argmax后):")
    print(network_output)

    # 模拟deeplab.py中的处理：+1转换为1-9
    final_output = network_output + 1

    print("最终输出(+1后):")
    print(final_output)

    print("输出值对应的类别:")
    classes = ["daolu", "dapeng", "gengdi", "guanmu", "jiansheyongdi", "lindi", "luodi", "yantang", "yvmidi"]
    for i, class_name in enumerate(classes):
        print(f"{i+1} -> {class_name}")
    print()

def test_predict_config():
    """测试预测配置"""
    print("=== 测试预测配置 ===")
    
    # 来自predict.py
    name_classes = ["daolu", "dapeng", "gengdi", "guanmu", "jiansheyongdi", "lindi", "luodi", "yantang", "yvmidi"]
    
    print(f"预测时的类别数量: {len(name_classes)}")
    print(f"类别列表: {name_classes}")
    
    # 验证与训练配置匹配
    num_classes = 9
    assert len(name_classes) == num_classes, f"预测类别数量与训练不匹配: {len(name_classes)} != {num_classes}"
    print("✓ 预测类别数量与训练配置匹配")
    print()

def test_deeplab_config():
    """测试DeepLab配置"""
    print("=== 测试 DeepLab 配置 ===")
    
    # 来自deeplab.py
    num_classes = 9
    print(f"DeepLab 类别数量: {num_classes}")
    
    # 验证颜色数量
    # 需要num_classes + 1个颜色（包括未分类区域）
    required_colors = num_classes + 1
    print(f"需要的颜色数量: {required_colors} (包括未分类区域)")
    print()

def test_label_mapping():
    """测试标签映射逻辑"""
    print("=== 测试标签映射逻辑 ===")
    
    # 模拟标签图像
    # 0: 未标注区域
    # 1-9: 九个类别
    label_img = np.array([
        [0, 1, 2, 3],
        [4, 5, 6, 7],
        [8, 9, 0, 1],
        [2, 3, 4, 5]
    ])
    
    print("原始标签图像:")
    print(label_img)
    
    # 模拟数据加载器中的处理逻辑
    png_processed = label_img.copy()
    png_processed[label_img == 0] = 255  # 未标注区域设为忽略标签
    png_processed[label_img > 0] = label_img[label_img > 0] - 1  # 1-9变成0-8
    
    print("\n处理后的标签（用于训练）:")
    print(png_processed)
    print("说明: 255表示忽略标签，0-8表示9个类别")
    
    # 模拟预测结果处理
    pred_result = np.array([
        [0, 1, 2, 3],
        [4, 5, 6, 7],
        [8, 0, 1, 2],
        [3, 4, 5, 6]
    ])  # 网络输出0-8
    
    print("\n网络预测结果（0-8）:")
    print(pred_result)
    
    # 转换为最终输出（1-9，0表示未分类）
    final_output = pred_result + 1
    
    print("\n最终输出（1-9，0表示未分类）:")
    print(final_output)
    print()

def main():
    """运行所有测试"""
    print("开始测试语义分割代码修改...")
    print("=" * 50)

    test_json_to_dataset_classes()
    test_train_config()
    test_dataloader_processing()
    test_prediction_output()
    test_predict_config()
    test_deeplab_config()
    test_label_mapping()

    print("=" * 50)
    print("所有测试完成！")
    print("\n修改总结:")
    print("1. 移除了背景类，现在只有9个标注类别")
    print("2. 标签值0表示未标注区域，1-9表示九个类别")
    print("3. 训练时将1-9映射为0-8，未标注区域设为忽略标签(255)")
    print("4. 预测时将网络输出0-8转换回1-9")
    print("5. 更新了所有相关配置文件的类别数量")

if __name__ == "__main__":
    main()
