# 语义分割代码修改说明

## 问题描述
原始代码默认将未标注的区域当作背景类处理，导致实际上有10个类别（9个标注类别 + 1个背景类）。用户希望只有9个标注类别，不将未标注区域作为一个独立的类别。

## 解决方案
修改了数据处理和模型配置，使得：
- 只有9个标注类别参与训练和预测
- 未标注区域在训练时被忽略（不参与损失计算）
- 预测时未标注区域显示为黑色（值为0）

## 修改的文件

### 1. `json_to_dataset.py`
**主要修改：**
- 移除了 `"_background_"` 类别
- 修改标签生成逻辑：
  - 未标注区域保持为0
  - 标注区域从1开始编号（1-9对应9个类别）

**关键代码：**
```python
# 修改前
classes = ["_background_","daolu", "dapeng", ...]
label_name_to_value = {'_background_': 0}

# 修改后  
classes = ["daolu", "dapeng", "gengdi", ...]
label_name_to_value = {}  # 不包含背景类
```

### 2. `train.py`
**修改：**
```python
# 修改前
num_classes = 10

# 修改后
num_classes = 9
```

### 3. `deeplab.py`
**主要修改：**
- 更新 `num_classes` 为 9
- 修改预测结果处理：将网络输出0-8转换为1-9
- 调整颜色映射：为9个类别+1个未分类区域设置颜色
- 修改计数功能：正确统计各类别像素数量

**关键代码：**
```python
# 预测结果处理
pr = pr.argmax(axis=-1)
pr = pr + 1  # 将0-8转换为1-9
```

### 4. `predict.py`
**修改：**
```python
# 修改前
name_classes = ["_background_","daolu", "dapeng", ...]

# 修改后
name_classes = ["daolu", "dapeng", "gengdi", ...]
```

### 5. `get_miou.py`
**修改：**
```python
# 修改前
name_classes = ["_background_","daolu", "dapeng", ...]

# 修改后
name_classes = ["daolu", "dapeng", "gengdi", ...]
```

### 6. `utils/dataloader.py`
**主要修改：**
- 修改标签处理逻辑：
  - 将标签值0（未标注）设为255（忽略标签）
  - 将标签值1-9转换为0-8（用于训练）

**关键代码：**
```python
# 处理标签：
# 原始标签：0=未标注，1-9=九个类别
# 训练标签：255=忽略，0-8=九个类别
png_processed = png.copy()
png_processed[png == 0] = 255  # 未标注区域设为忽略标签
png_processed[png > 0] = png[png > 0] - 1  # 1-9变成0-8

# 返回处理后的标签
return jpg, png_processed, seg_labels
```

## 标签映射关系

### 数据生成阶段（json_to_dataset.py）
- 未标注区域 → 0
- 9个类别 → 1, 2, 3, 4, 5, 6, 7, 8, 9

### 训练阶段（dataloader.py）
- 未标注区域：0 → 255（忽略标签）
- 9个类别：1-9 → 0-8（网络训练标签）

### 预测阶段（deeplab.py）
- 网络输出：0-8
- 最终输出：1-9（1-9对应9个类别）

## 类别对应关系
| 序号 | 类别名称 | 标签文件中的值 | 训练时的值 | 预测输出值 |
|------|----------|----------------|------------|------------|
| 1 | daolu | 1 | 0 | 1 |
| 2 | dapeng | 2 | 1 | 2 |
| 3 | gengdi | 3 | 2 | 3 |
| 4 | guanmu | 4 | 3 | 4 |
| 5 | jiansheyongdi | 5 | 4 | 5 |
| 6 | lindi | 6 | 5 | 6 |
| 7 | luodi | 7 | 6 | 7 |
| 8 | yantang | 8 | 7 | 8 |
| 9 | yvmidi | 9 | 8 | 9 |
| - | 未标注区域 | 0 | 255(忽略) | 理论上不会出现 |

## 验证

运行 `test_modifications.py` 可以验证所有修改是否正确。该脚本会测试：
1. 类别定义的一致性
2. 标签处理逻辑
3. 预测输出处理
4. 各配置文件的匹配性

## 使用方法

### 1. 重新生成数据集
如果您有新的标注数据，运行：
```bash
python json_to_dataset.py
```

### 2. 训练模型
```bash
python train.py
```
确保 `num_classes = 9`

### 3. 预测
```bash
python predict.py
```

### 4. 评估模型
```bash
python get_miou.py
```

## 注意事项

1. **重新训练**：由于修改了类别数量和标签处理逻辑，需要重新训练模型
2. **预训练权重**：如果使用预训练权重，需要确保最后一层的输出维度匹配（9个类别）
3. **数据一致性**：确保所有标注数据都使用新的标签映射关系
4. **评估指标**：mIoU等评估指标现在基于9个类别计算
5. **颜色映射**：预测结果可视化时，确保有足够的颜色用于9个类别的显示
```

### 4. 评估
```bash
python get_miou.py
```

## 注意事项

1. **重新训练**：由于类别数量从10改为9，需要重新训练模型
2. **预训练权重**：如果使用预训练权重，需要确保最后一层的输出维度匹配（9个类别）
3. **数据一致性**：确保所有标注数据都使用相同的类别名称
4. **可视化**：未标注区域在可视化时显示为黑色

## 验证修改
运行测试脚本验证修改是否正确：
```bash
python test_modifications.py
```

这将验证所有配置是否一致，标签映射是否正确。
